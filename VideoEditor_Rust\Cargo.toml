[package]
name = "VideoEditor_Rust"
version = "0.1.0"
edition = "2024"

[dependencies]
# 序列化/反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 异步运行时
tokio = { version = "1", features = ["full", "signal"] }
tokio-util = "0.7"  # 取消令牌支持

# 日志
log = "0.4"
env_logger = "0.10"

# 工具
uuid = { version = "1.4", features = ["v4", "serde"] }
thiserror = "1.0"  # 错误处理
anyhow = "1.0"     # 错误处理
tempfile = "3.8"   # 临时文件处理
path-absolutize = "3.1" # 路径处理

# 进度报告
futures = "0.3"

# TCP Socket通信 - 使用tokio内置支持，无需额外依赖

# 系统信息
num_cpus = "1.16"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }