// ******************************************************
// 文件名: SpeedEffect.cs
// 功能描述: 视频变速效果实现
// 创建日期: 2023-12-01
// 最后修改: 2023-12-01
// 主要职责: 
//   1. 提供视频变速效果的卡片定义
//   2. 实现视频变速的对话框显示和处理逻辑
// ******************************************************

using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Layout;
using Avalonia.Media;
using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using ECutPro.Views.VideoEffectDialogs;
using LiteDB;

namespace ECutPro.VideoEffects
{
    /// <summary>
    /// 视频变速效果
    /// </summary>
    public partial class SpeedEffect : VideoEffect
    {
        // ====================== 内部设置类 ======================
        
        /// <summary>
        /// 变速效果设置
        /// </summary>
        public partial class SpeedEffectSettings : ObservableObject
        {
            [BsonId]
            public string EffectTag { get; set; } = "video-speed";

            [ObservableProperty]
            private bool _isEnabled = false;
            
            [ObservableProperty]
            private double _speedValue = 1.0;
            
            [ObservableProperty]
            private double _minSpeedValue = 1.0;
            
            [ObservableProperty]
            private double _maxSpeedValue = 1.25;
            
            [ObservableProperty]
            private bool _enableRandomSpeed = false;
            
            [ObservableProperty]
            private bool _keepAudioOriginal = false;
        }
        
        // ====================== 私有字段 ======================

        /// <summary>
        /// 效果设置对象
        /// </summary>
        public SpeedEffectSettings Settings { get; set; } = new SpeedEffectSettings();
        
        // ====================== 重写属性 ======================
        
        /// <summary>
        /// 卡片标题
        /// </summary>
        public override string Title => "视频变速";
        
        /// <summary>
        /// 卡片描述
        /// </summary>
        public override string Description => "调整视频播放速度";
        
        /// <summary>
        /// 卡片图标路径数据
        /// </summary>
        public override string IconPath => "M7,5 L15,9 L7,13 Z";
        
        /// <summary>
        /// 卡片标识
        /// </summary>
        public override string Tag => "video-speed";
        
        /// <summary>
        /// 对话框宽度
        /// </summary>
        public override double DialogWidth => 490;
        
        /// <summary>
        /// 对话框高度
        /// </summary>
        public override double DialogHeight => 200;
        
        // ====================== 设置持久化 ======================

        /// <summary>
        /// 保存设置到存储
        /// </summary>
        public override void SaveSettingsToStorage()
        {
            // 同步启用状态到设置对象
            Settings.IsEnabled = IsEnabled;
            App.UserPreferences?.SaveEffectSettings(Settings);
        }
        
        /// <summary>
        /// 从存储加载设置
        /// </summary>
        public override void LoadSettingsFromStorage()
        {
            var savedSettings = App.UserPreferences?.GetEffectSettings<SpeedEffectSettings>(Settings.EffectTag);
            if (savedSettings != null)
            {
                Settings = savedSettings;
                IsEnabled = savedSettings.IsEnabled;
            }
        }
        
        // ====================== 重写方法 ======================
        
        /// <summary>
        /// 预览效果
        /// </summary>
        protected override void PreviewEffect()
        {
            // 基础预览实现
            App.ShowToast("预览功能正在开发中，敬请期待！");
        }
        
        /// <summary>
        /// 创建对话框内容
        /// </summary>
        protected override Control CreateDialogContent()
        {
            // 创建内容区域
            var contentArea = new Grid
            {
                RowDefinitions = new RowDefinitions("Auto,Auto"),
                ColumnDefinitions = new ColumnDefinitions("Auto,*")
            };
            
            // 添加速度范围标签
            var rangeLabel = new TextBlock
            {
                Text = "速度范围:",
                VerticalAlignment = VerticalAlignment.Center,
                Foreground = new SolidColorBrush(Colors.White),
                Margin = new Thickness(0, 0, 10, 0)
            };
            Grid.SetRow(rangeLabel, 0);
            Grid.SetColumn(rangeLabel, 0);
            contentArea.Children.Add(rangeLabel);
            
            // 创建范围输入面板
            var inputPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Spacing = 8
            };
            
            // 最小速度输入框
            var minSpeedBox = new TextBox
            {
                Text = Settings.MinSpeedValue.ToString("F1"),
                Width = 60
            };
            
            minSpeedBox.TextChanged += (s, e) =>
            {
                if (double.TryParse(minSpeedBox.Text, out double value))
                {
                    Settings.MinSpeedValue = value;
                }
            };
            
            // 范围分隔符
            var separator = new TextBlock
            {
                Text = "-",
                VerticalAlignment = VerticalAlignment.Center,
                Foreground = new SolidColorBrush(Colors.White),
                Margin = new Thickness(5, 0)
            };
            
            // 最大速度输入框
            var maxSpeedBox = new TextBox
            {
                Text = Settings.MaxSpeedValue.ToString("F1"),
                Width = 60
            };
            
            maxSpeedBox.TextChanged += (s, e) =>
            {
                if (double.TryParse(maxSpeedBox.Text, out double value))
                {
                    Settings.MaxSpeedValue = value;
                }
            };
            
            // 添加所有元素到输入面板
            inputPanel.Children.Add(minSpeedBox);
            inputPanel.Children.Add(separator);
            inputPanel.Children.Add(maxSpeedBox);
            
            // 添加复选框到输入面板
            
            // 随机变速复选框
            var randomCheckBox = new CheckBox
            {
                Content = "随机变速",
                IsChecked = Settings.EnableRandomSpeed,
                Foreground = new SolidColorBrush(Colors.White),
                Margin = new Thickness(20, 0, 5, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            
            // 添加工具提示
            ToolTip.SetTip(randomCheckBox, "在指定范围内随机设置播放速度");
            ToolTip.SetShowDelay(randomCheckBox, 100);
            
            randomCheckBox.IsCheckedChanged += (s, e) =>
            {
                Settings.EnableRandomSpeed = randomCheckBox.IsChecked ?? false;
                
                if (Settings.EnableRandomSpeed)
                {
                    var random = new Random();
                    double randomSpeed = Settings.MinSpeedValue + random.NextDouble() * (Settings.MaxSpeedValue - Settings.MinSpeedValue);
                    randomSpeed = Math.Round(randomSpeed * 10) / 10;
                    Settings.SpeedValue = randomSpeed;
                }
                else
                {
                    Settings.SpeedValue = Settings.MinSpeedValue;
                }
            };
            
            // 不变速音频复选框
            var keepAudioCheckBox = new CheckBox
            {
                Content = "不变速音频",
                IsChecked = Settings.KeepAudioOriginal,
                Foreground = new SolidColorBrush(Colors.White),
                Margin = new Thickness(5, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            
            // 添加工具提示
            ToolTip.SetTip(keepAudioCheckBox, "保持音频原速播放，不随视频变速");
            ToolTip.SetShowDelay(keepAudioCheckBox, 100);
            
            keepAudioCheckBox.IsCheckedChanged += (s, e) =>
            {
                Settings.KeepAudioOriginal = keepAudioCheckBox.IsChecked ?? false;
            };
            
            // 添加复选框到输入面板
            inputPanel.Children.Add(randomCheckBox);
            inputPanel.Children.Add(keepAudioCheckBox);
            
            Grid.SetRow(inputPanel, 0);
            Grid.SetColumn(inputPanel, 1);
            contentArea.Children.Add(inputPanel);
            
            // 为输入框添加工具提示
            ToolTip.SetTip(minSpeedBox, "设置最小播放速度");
            ToolTip.SetShowDelay(minSpeedBox, 100);
            
            ToolTip.SetTip(maxSpeedBox, "设置最大播放速度");
            ToolTip.SetShowDelay(maxSpeedBox, 100);
            
            return contentArea;
        }
    }
} 













