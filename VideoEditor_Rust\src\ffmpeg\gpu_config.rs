use crate::ffmpeg::FFmpegCommandBuilder;
use crate::models::ProcessingOptions;
use std::process::Command;
use log::{warn};
use std::path::Path;

/// GPU配置管理器
pub struct GpuConfigManager;

impl GpuConfigManager {
    /// 检查GPU是否可用 - 扁平化风格
    ///
    /// # 参数
    /// * `gpu_type` - GPU类型（amd、nvidia、intel）
    /// * `ffmpeg_path` - FFmpeg可执行文件路径
    ///
    /// # 返回
    /// * `GpuCheckMessage` - 完整的GPU检查结果消息
    pub fn check_gpu_available(gpu_type: &str, ffmpeg_path: &Path) -> crate::ipc::UniversalMessage {
        let gpu_name = gpu_type.to_uppercase();

        // 早返回 - 检查GPU类型是否支持
        if !["amd", "nvidia", "intel"].contains(&gpu_type) {
            return crate::ipc::error(format!("不支持的GPU类型: {}，仅支持 NVIDIA、AMD、Intel", gpu_name));
        }

        let encoder = Self::get_encoder_name(gpu_type);

        // 早返回 - 检查编码器是否可用
        if !Self::check_encoder_available(ffmpeg_path, encoder) {
            warn!("{}编码器不可用", encoder);
            return crate::ipc::error(format!("{} GPU加速不可用，未找到相关编码器", gpu_name));
        }

        // 早返回 - 检查硬件是否可用
        match Self::test_hardware_encoding(ffmpeg_path, encoder) {
            Ok(true) => {
                crate::ipc::success(serde_json::json!({
                    "gpu_type": gpu_type,
                    "available": true,
                    "message": format!("{} GPU加速可用", gpu_name)
                }))
            }
            Ok(false) => {
                crate::ipc::error(format!("{} GPU加速不可用，硬件不支持或驱动未正确安装", gpu_name))
            }
            Err(_) => {
                crate::ipc::error(format!("{} GPU加速检查失败，请检查FFmpeg配置", gpu_name))
            }
        }
    }

    /// 检查编码器是否可用 - 扁平化风格
    fn check_encoder_available(ffmpeg_path: &Path, encoder: &str) -> bool {
        let output = match Command::new(ffmpeg_path)
            .args(&["-h", &format!("encoder={}", encoder)])
            .output() {
                Ok(output) => output,
                Err(_) => return false,
            };

        let success = output.status.success();
        let stdout = String::from_utf8_lossy(&output.stdout);
        success && (stdout.contains(encoder) || stdout.contains("Encoder"))
    }

    /// 测试硬件编码是否可用 - 扁平化风格
    fn test_hardware_encoding(ffmpeg_path: &Path, encoder: &str) -> Result<bool, std::io::Error> {
        let output = Command::new(ffmpeg_path)
            .args(&[
                "-f", "lavfi",
                "-i", "color=black:size=256x256:duration=0.1",
                "-frames:v", "1",
                "-c:v", encoder,
                "-f", "null",
                "-"
            ])
            .output()?;

        Ok(output.status.success())
    }
    
    /// 获取编码器名称
    pub fn get_encoder_name(gpu_type: &str) -> &'static str {
        match gpu_type {
            "amd" => "h264_amf",
            "nvidia" => "h264_nvenc",
            "intel" => "h264_qsv",
            _ => "libx264", // 默认CPU编码器
        }
    }
    
    /// 应用输出格式参数（在GPU加速模式下）
    pub fn apply_format_params(cmd_builder: &mut FFmpegCommandBuilder, format_index: i32) {
        match format_index {
            0 => {}, // 原格式，不需要额外设置
            1 => { cmd_builder.output_options(&["-pix_fmt", "yuv420p"]); }, // MP4
            2 => { cmd_builder.output_options(&["-f", "mov"]); }, // MOV
            3 => { cmd_builder.output_options(&["-f", "matroska"]); }, // MKV
            _ => {} // 默认不处理
        }
    }
    
    /// 应用GPU加速配置
    pub fn apply_gpu_config(
        cmd_builder: &mut FFmpegCommandBuilder,
        options: &ProcessingOptions,
        _has_bitrate: bool,
        input_path: Option<&str> // 新增参数，用于获取原视频码率
    ) {
        // 添加通用的GPU加速参数
        cmd_builder.output_options(&["-extra_hw_frames", "3"]);
        
        // 应用输出格式参数
        Self::apply_format_params(cmd_builder, options.output_format_index);

        // 处理质量设置 - 统一在这里处理码率
        if options.output_quality_index == 0 {
            // 原质量：尝试获取原视频码率，如果获取不到则使用默认高质量码率
            // 使用FFmpeg分析器的便捷方法获取码率
            let bitrate_kbps = input_path
                .and_then(|path| crate::ffmpeg::FFmpegAnalyzer::get_bitrate(path))
                .unwrap_or(5000); // 默认5Mbps，适合大多数高清视频
            
            // 设置码率参数
            cmd_builder.output_options(&[
                "-b:v", &format!("{}k", bitrate_kbps),
                "-maxrate", &format!("{}k", bitrate_kbps * 2),
                "-bufsize", &format!("{}k", bitrate_kbps * 2)
            ]);
        }

        // 应用GPU特定配置
        match options.gpu_acceleration_index {
            1 => Self::configure_amd(cmd_builder, options.output_quality_index),
            2 => Self::configure_nvidia(cmd_builder, options.output_quality_index),
            3 => Self::configure_intel(cmd_builder, options.output_quality_index),
            _ => {} // 不使用GPU加速
        }
    }

    /// 配置AMD GPU加速 - 行业最佳实践
    fn configure_amd(cmd_builder: &mut FFmpegCommandBuilder, quality_index: i32) {
        // 添加AMD特定的硬件加速参数
        cmd_builder.add_hwaccel_options("amf", None, None);
        cmd_builder.output_options(&["-c:v", "h264_amf"]);

        // 应用AMD质量设置 - 基于AMD AMF官方推荐
        let quality_options = match quality_index {
            0 => vec!["-rc", "vbr", "-quality", "quality"], // 原质量：高质量VBR
            1 => {
                // AMD AMF的最高质量设置，接近无损
                log::warn!("AMD GPU不支持真正无损编码，使用最高质量设置");
                vec!["-rc", "cqp", "-qp_i", "1", "-qp_p", "1", "-qp_b", "1", "-quality", "quality"]
            },
            2 => vec!["-rc", "vbr", "-qp_i", "18", "-qp_p", "20", "-qp_b", "22", "-quality", "quality"], // 高质量
            3 => vec!["-rc", "vbr", "-qp_i", "23", "-qp_p", "25", "-qp_b", "27", "-quality", "balanced"], // 平衡
            4 => vec!["-rc", "vbr", "-qp_i", "28", "-qp_p", "30", "-qp_b", "32", "-quality", "speed"], // 快速
            _ => vec!["-rc", "vbr", "-quality", "balanced"], // 默认平衡
        };

        cmd_builder.output_options(&quality_options);
    }

    /// 配置NVIDIA GPU加速 - 行业最佳实践
    fn configure_nvidia(cmd_builder: &mut FFmpegCommandBuilder, quality_index: i32) {
        // 添加NVIDIA特定的硬件加速参数
        cmd_builder.add_hwaccel_options("cuda", None, None);
        cmd_builder.output_options(&["-c:v", "h264_nvenc"]);

        // 应用NVIDIA质量设置 - 基于NVIDIA官方推荐
        let quality_options = match quality_index {
            0 => vec!["-preset", "p6", "-tune", "hq", "-rc", "vbr"], // 原质量：保持码率控制
            1 => {
                // 注意：NVENC硬件编码器不支持真正的数学无损
                // 使用最高质量设置，接近无损效果
                log::warn!("NVIDIA GPU不支持真正无损编码，使用最高质量设置");
                vec!["-preset", "p7", "-tune", "hq", "-rc", "constqp", "-qp", "1"]
            },
            2 => vec!["-preset", "p6", "-tune", "hq", "-rc", "vbr", "-cq", "18", "-spatial_aq", "1"], // 高质量
            3 => vec!["-preset", "p4", "-tune", "hq", "-rc", "vbr", "-cq", "23", "-spatial_aq", "1"], // 平衡
            4 => vec!["-preset", "p2", "-tune", "ll", "-rc", "vbr", "-cq", "28"], // 快速
            _ => vec!["-preset", "p4", "-tune", "hq", "-rc", "vbr", "-cq", "23"], // 默认平衡
        };

        cmd_builder.output_options(&quality_options);
    }

    /// 配置Intel GPU加速 - 行业最佳实践
    fn configure_intel(cmd_builder: &mut FFmpegCommandBuilder, quality_index: i32) {
        // 添加Intel特定的硬件加速参数
        cmd_builder.add_hwaccel_options("qsv", None, None);
        cmd_builder.output_options(&["-c:v", "h264_qsv"]);

        // 应用Intel质量设置 - 基于Intel QSV官方推荐
        let quality_options = match quality_index {
            0 => vec!["-preset", "medium"], // 原质量：使用中等预设，码率已设置
            1 => {
                // Intel QSV的最高质量设置
                log::warn!("Intel GPU不支持真正无损编码，使用最高质量设置");
                vec!["-preset", "veryslow", "-global_quality", "1", "-look_ahead", "1"]
            },
            2 => vec!["-preset", "slow", "-global_quality", "15", "-look_ahead", "1"], // 高质量
            3 => vec!["-preset", "medium", "-global_quality", "23"], // 平衡
            4 => vec!["-preset", "fast", "-global_quality", "30"], // 快速
            _ => vec!["-preset", "medium", "-global_quality", "23"], // 默认
        };

        cmd_builder.output_options(&quality_options);
    }
} 