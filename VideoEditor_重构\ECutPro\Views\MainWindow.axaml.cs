// ******************************************************
// 文件名: MainWindow.axaml.cs
// 功能描述: 主窗口视图类
// 创建日期: 2023-12-01
// 最后修改: 2023-12-01
// 主要职责: 
//   1. 处理主窗口UI交互和事件
//   2. 管理功能卡片的点击和状态切换
//   3. 提供窗口控制功能（最小化、关闭等）
// ******************************************************

using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using System.ComponentModel;
using ECutPro.ViewModels;
using ECutPro.VideoEffects;

namespace ECutPro.Views;

/// <summary>
/// 主窗口视图类，负责UI交互
/// </summary>
public partial class MainWindow : Window
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public MainWindow()
    {
        InitializeComponent();
#if DEBUG
        this.AttachDevTools();
#endif
        // 设置视图模型
        DataContext = App.MainViewModel;

        // 初始化加载动画
        InitializeLoadingAnimation();
    }

    /// <summary>
    /// 初始化组件
    /// </summary>
    private void InitializeComponent()
    {
        AvaloniaXamlLoader.Load(this);
    }

    /// <summary>
    /// 初始化加载动画
    /// </summary>
    private void InitializeLoadingAnimation()
    {
        var loadingAnimation = this.FindControl<ECutPro.Controls.FrameAnimationControl>("LoadingAnimation");
        if (loadingAnimation != null)
        {
            // 启动默认加载动画，60fps
            loadingAnimation.Play(60.0);
        }
    }
    
    // 获取ListBox控件引用
    private ListBox? GetVideoListBox() => this.FindControl<ListBox>("VideoListBox");

    /// <summary>
    /// 最小化按钮点击事件
    /// </summary>
    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        this.WindowState = WindowState.Minimized;
    }

    /// <summary>
    /// 最大化/还原按钮点击事件 (已禁用)
    /// </summary>
    private void MaximizeButton_Click(object sender, RoutedEventArgs e)
    {
        // 窗口不允许最大化，此方法保留但不执行任何操作
    }

    /// <summary>
    /// 关闭按钮点击事件
    /// </summary>
    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        this.Close();
    }
    
    /// <summary>
    /// 窗口关闭事件 - 确保取消正在进行的任务
    /// </summary>
    protected override void OnClosing(WindowClosingEventArgs e)
    {
        base.OnClosing(e);

        // 检查是否有正在进行的任务
        if (DataContext is MainWindowViewModel viewModel && viewModel.IsProcessing)
        {
            // 使用TcpService取消任务
            _ = App.TcpService.CancelAllTasksAsync();

            // 记录日志
            System.Diagnostics.Debug.WriteLine("窗口关闭时取消了正在进行的处理任务");
        }
    }

    /// <summary>
    /// 拖拽区域鼠标按下事件
    /// </summary>
    private void DragArea_PointerPressed(object sender, PointerPressedEventArgs e)
    {
        if (e.GetCurrentPoint(this).Properties.IsLeftButtonPressed)
        {
            this.BeginMoveDrag(e);
        }
    }
    
    /// <summary>
    /// 效果卡片点击事件处理
    /// </summary>
    private async void OnEffectCardTapped(object sender, TappedEventArgs e)
    {
        if (sender is Border border && border.DataContext is VideoEffect effect)
        {
            // 显示效果对话框
            await effect.ShowDialogAsync();
        }
    }
    
    /// <summary>
    /// 处理多选文件的移除操作
    /// </summary>
    private void RemoveSelectedVideos_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            var selectedItems = GetVideoListBox()?.SelectedItems;
            viewModel.RemoveSelectedVideosCommand.Execute(selectedItems);
        }
    }

    /// <summary>
    /// 清空所有视频文件
    /// </summary>
    private void ClearAllVideos_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            viewModel.ClearAllVideosCommand.Execute(null);
        }
    }

    /// <summary>
    /// 处理按钮点击事件 - 根据当前状态执行开始或取消处理
    /// </summary>
    private async void ProcessButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            if (viewModel.IsProcessing)
            {
                await viewModel.CancelProcessingCommand.ExecuteAsync(null);
            }
            else
            {
                await viewModel.StartProcessingCommand.ExecuteAsync(null);
            }
        }
    }





}


