{"format": 1, "restore": {"E:\\编程\\E剪Pro\\客户端\\批量视频编辑器\\重构版本\\VideoEditor_重构\\ECutPro\\ECutPro.csproj": {}}, "projects": {"E:\\编程\\E剪Pro\\客户端\\批量视频编辑器\\重构版本\\VideoEditor_重构\\ECutPro\\ECutPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\编程\\E剪Pro\\客户端\\批量视频编辑器\\重构版本\\VideoEditor_重构\\ECutPro\\ECutPro.csproj", "projectName": "ECutPro", "projectPath": "E:\\编程\\E剪Pro\\客户端\\批量视频编辑器\\重构版本\\VideoEditor_重构\\ECutPro\\ECutPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\编程\\E剪Pro\\客户端\\批量视频编辑器\\重构版本\\VideoEditor_重构\\ECutPro\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\APP\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.3.2, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.3.2, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[11.3.2, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.3.2, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.3.2, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "LiteDB": {"target": "Package", "version": "[5.0.21, )"}, "Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "runtime.win-x64.Microsoft.DotNet.ILCompiler", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}