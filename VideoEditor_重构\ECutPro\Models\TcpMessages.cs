// ******************************************************
// 文件名: TcpMessages.cs
// 功能描述: TCP通信消息模型 - 简洁优雅的强类型设计
// 创建日期: 2023-12-01
// 最后修改: 2023-12-01
// 主要职责:
//   1. 定义TCP通信的强类型消息模型
//   2. 完全匹配Rust后端消息结构
//   3. 支持AOT编译，零反射依赖
//   4. 遵循.NET最佳实践和命名规范
// ******************************************************

using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using ECutPro.VideoEffects;

namespace ECutPro.Models;

/// <summary>
/// TCP消息容器 - 所有TCP通信的基础结构
/// </summary>
public sealed class TcpMessage
{
    /// <summary>
    /// 消息类型标识
    /// </summary>
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 消息数据载荷
    /// </summary>
    [JsonPropertyName("data")]
    public object? Data { get; set; }
}

/// <summary>
/// 视频批量处理命令 - 匹配Rust后端ProcessRequest
/// </summary>
public sealed class ProcessVideosCommand
{
    /// <summary>
    /// 输入视频文件路径列表
    /// </summary>
    [JsonPropertyName("input_paths")]
    public List<string> InputPaths { get; set; } = [];

    /// <summary>
    /// 输出目录路径
    /// </summary>
    [JsonPropertyName("output_dir")]
    public string OutputDir { get; set; } = string.Empty;

    /// <summary>
    /// 启用的视频效果列表
    /// </summary>
    [JsonPropertyName("effects")]
    public List<EffectParam> Effects { get; set; } = [];

    /// <summary>
    /// 处理选项配置
    /// </summary>
    [JsonPropertyName("options")]
    public ProcessingOptions Options { get; set; } = new();
}

/// <summary>
/// 预览效果命令
/// </summary>
public sealed class PreviewEffectCommand
{
    /// <summary>
    /// 视频文件路径
    /// </summary>
    [JsonPropertyName("video_path")]
    public string VideoPath { get; set; } = string.Empty;

    /// <summary>
    /// 要预览的效果参数
    /// </summary>
    [JsonPropertyName("effect")]
    public EffectParam Effect { get; set; } = new();
}

/// <summary>
/// 视频效果参数 - 匹配Rust后端EffectParam结构
/// </summary>
public sealed class EffectParam
{
    /// <summary>
    /// 效果唯一标识
    /// </summary>
    [JsonPropertyName("effect_id")]
    public string EffectId { get; set; } = string.Empty;

    /// <summary>
    /// 效果类型名称
    /// </summary>
    [JsonPropertyName("effect_type")]
    public string EffectType { get; set; } = string.Empty;

    /// <summary>
    /// 效果参数JSON数据
    /// </summary>
    [JsonPropertyName("params")]
    public JsonElement Params { get; set; }
}



/// <summary>
/// 通用响应消息结构 - 匹配Rust后端UniversalMessage
/// </summary>
public sealed class UniversalMessage
{
    /// <summary>
    /// 操作是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 响应数据载荷
    /// </summary>
    [JsonPropertyName("data")]
    public JsonElement Data { get; set; }

    /// <summary>
    /// 用户友好的消息文本
    /// </summary>
    [JsonPropertyName("message")]
    public string? Message { get; set; }

    /// <summary>
    /// 获取任务ID（通常是文件路径）
    /// </summary>
    public string GetTaskId() =>
        Data.TryGetProperty("task_id", out var taskId) ? taskId.GetString() ?? "" : "";

    /// <summary>
    /// 获取进度描述文本
    /// </summary>
    public string? GetProgress() =>
        Data.TryGetProperty("progress", out var progress) ? progress.GetString() : null;

    /// <summary>
    /// 获取全局进度百分比
    /// </summary>
    public float GetGlobalProgress() =>
        Data.TryGetProperty("global_progress", out var progress) ? progress.GetSingle() : 0f;

    /// <summary>
    /// 判断是否为进度消息
    /// </summary>
    public bool IsProgressMessage() => Data.TryGetProperty("progress", out _);

    /// <summary>
    /// 判断是否为状态消息
    /// </summary>
    public bool IsStatusMessage() => Data.TryGetProperty("status", out _);
}



/// <summary>
/// 视频处理结果 - 本地使用的结果封装
/// </summary>
public sealed class VideoProcessingResult
{
    /// <summary>
    /// 处理是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息（失败时）
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 已处理的视频数量
    /// </summary>
    public int ProcessedCount { get; set; }

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="processedCount">已处理数量</param>
    /// <returns>成功结果</returns>
    public static VideoProcessingResult Success(int processedCount) => new()
    {
        IsSuccess = true,
        ProcessedCount = processedCount
    };

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>失败结果</returns>
    public static VideoProcessingResult Failed(string errorMessage) => new()
    {
        IsSuccess = false,
        ErrorMessage = errorMessage
    };

    /// <summary>
    /// 创建取消结果
    /// </summary>
    /// <returns>取消结果</returns>
    public static VideoProcessingResult Cancelled() => new()
    {
        IsSuccess = false,
        ErrorMessage = "处理已取消"
    };
}
