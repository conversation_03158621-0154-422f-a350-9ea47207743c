<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:dialogs="using:ECutPro.Views.VideoEffectDialogs"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="ECutPro.Views.VideoEffectDialogs.EffectDialogWindow"
        x:DataType="dialogs:EffectDialogWindow"
        TransparencyLevelHint="Transparent"
        Background="#40000000"
        WindowStartupLocation="CenterOwner"
        SystemDecorations="None"
        CanResize="False"
        ExtendClientAreaToDecorationsHint="True"
        ExtendClientAreaTitleBarHeightHint="-1">
    
    <Window.Styles>
        <!-- 添加淡入动画 -->
        <Style Selector="Window">
            <Style.Animations>
                <Animation Duration="0:0:0.1">
                    <KeyFrame Cue="0%">
                        <Setter Property="Opacity" Value="0.0"/>
                    </KeyFrame>
                    <KeyFrame Cue="100%">
                        <Setter Property="Opacity" Value="1.0"/>
                    </KeyFrame>
                </Animation>
            </Style.Animations>
        </Style>
    </Window.Styles>
    
    <Grid>
        <Border Name="DialogBorder"
                Background="#242424"
                CornerRadius="12"
                BorderThickness="0"
                BoxShadow="0 4 25 0 #50000000"
                Width="500"
                Height="350"
                HorizontalAlignment="Center"
                VerticalAlignment="Center">
            <Grid RowDefinitions="40,*,40">
                <!-- 标题栏 -->
                <Border Background="#2A2A2A"
                        CornerRadius="12,12,0,0"
                        IsHitTestVisible="True">
                    <Grid ColumnDefinitions="Auto,*,Auto,Auto">
                        <!-- 标题文本 -->
                        <TextBlock Text="{Binding Title}"
                                   HorizontalAlignment="Left"
                                   VerticalAlignment="Center"
                                   Foreground="#DDDDDD"
                                   FontSize="14"
                                   Margin="15,0,0,0"
                                   IsHitTestVisible="False" />
                        
                        <!-- 预览按钮 -->
                        <Button Name="PreviewButton"
                                Content="预览"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                Foreground="#30C9B0"
                                Margin="0,0,10,0"
                                Padding="8,2"
                                FontSize="12"
                                BorderThickness="1"
                                BorderBrush="#30C9B0"
                                CornerRadius="3"
                                Grid.Column="2">
                            <Button.Cursor>
                                <Cursor>Hand</Cursor>
                            </Button.Cursor>
                        </Button>
                        
                        <!-- 关闭按钮 -->
                        <Button Name="CloseButton"
                                Content="×"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Background="Transparent"
                                Foreground="#AAAAAA"
                                Margin="0,0,15,0"
                                Padding="8,0"
                                FontSize="16"
                                Grid.Column="3">
                            <Button.Cursor>
                                <Cursor>Hand</Cursor>
                            </Button.Cursor>
                        </Button>
                    </Grid>
                </Border>
                
                <!-- 内容区域 -->
                <ScrollViewer Grid.Row="1"
                              VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled"
                              Margin="20,10,20,10">
                    <ContentControl Name="ContentArea"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center" />
                </ScrollViewer>
                
                <!-- 按钮区域 -->
                <Grid ColumnDefinitions="*,*"
                      IsHitTestVisible="True"
                      Grid.Row="2">
                    <!-- 确认按钮 -->
                    <Button Name="ConfirmButton"
                            Content="启用功能"
                            Background="#2A2A2A"
                            BorderThickness="0"
                            CornerRadius="0,0,0,12"
                            FontSize="13"
                            FontWeight="Normal"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Height="40"
                            Foreground="White"
                            Grid.Column="0">
                        <Button.Cursor>
                            <Cursor>Hand</Cursor>
                        </Button.Cursor>
                    </Button>
                    
                    <!-- 取消按钮 -->
                    <Button Name="CancelButton"
                            Content="关闭功能"
                            Background="#2A2A2A"
                            BorderThickness="0"
                            CornerRadius="0,0,12,0"
                            FontSize="13"
                            FontWeight="Normal"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Height="40"
                            Foreground="White"
                            Grid.Column="1">
                        <Button.Cursor>
                            <Cursor>Hand</Cursor>
                        </Button.Cursor>
                    </Button>
                    
                    <!-- 中间分割线 -->
                    <Border Width="1"
                            Background="#3A3A3A"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Stretch"
                            IsHitTestVisible="False"
                            Grid.Column="0" />
                </Grid>
            </Grid>
        </Border>
    </Grid>
</Window> 