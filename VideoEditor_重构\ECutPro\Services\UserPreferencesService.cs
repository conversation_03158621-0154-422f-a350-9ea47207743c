// ******************************************************
// 文件名: UserPreferencesService.cs
// 功能描述: 用户偏好设置服务
// 创建日期: 2023-12-01
// 最后修改: 2023-12-01
// 主要职责: 
//   1. 管理用户偏好设置的持久化存储
//   2. 提供数据访问
//   3. 自动同步设置变更
// ******************************************************

using System;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Collections.Generic;
using ECutPro.Models;
using LiteDB;

namespace ECutPro.Services
{
    /// <summary>
    /// 效果设置记录类 - 用于存储单个效果的设置
    /// </summary>
    public class EffectSettingsRecord
    {
        /// <summary>
        /// 效果标识，作为文档的唯一ID
        /// </summary>
        [BsonId]
        public required string EffectTag { get; set; }
        
        /// <summary>
        /// 效果是否启用
        /// </summary>
        public bool IsEnabled { get; set; }
        
        /// <summary>
        /// 效果的具体设置参数
        /// </summary>
        public Dictionary<string, object> Settings { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 用户偏好设置服务 - 负责管理用户配置的持久化
    /// </summary>
    public sealed class UserPreferencesService : IDisposable
    {
        private bool _disposed;
        private LiteDatabase? _db;
        private readonly string _dbPath;
        
        // 集合名称常量
        private const string PROCESSING_OPTIONS_COLLECTION = "processingOptions";
        private const string VIDEO_EFFECT_SETTINGS_COLLECTION = "videoEffectSettings";
        
        /// <summary>
        /// 处理选项
        /// </summary>
        public ProcessingOptions ProcessingOptions { get; private set; } = new ProcessingOptions();
        
        /// <summary>
        /// 构造函数 - 初始化数据库和设置
        /// </summary>
        public UserPreferencesService()
        {
            // 获取应用数据路径
            _dbPath = GetDatabasePath();
            
            // 初始化数据库
            InitializeDatabase();
            
            // 加载初始设置
            LoadPreferences();
        }
        
        /// <summary>
        /// 获取数据库路径
        /// </summary>
        private string GetDatabasePath()
        {
            string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            string appFolder = Path.Combine(appDataPath, "ECutPro");
            
            // 确保目录存在
            Directory.CreateDirectory(appFolder);
            
            return Path.Combine(appFolder, "user_preferences.db");
        }
        
        /// <summary>
        /// 初始化数据库连接
        /// </summary>
        private void InitializeDatabase()
        {
            try
            {
                // 创建数据库连接，不使用加密
                _db = new LiteDatabase(_dbPath);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"初始化数据库失败: {ex.Message}");
                
                // 如果数据库打开失败，尝试删除可能损坏的数据库文件并重新创建
                if (File.Exists(_dbPath)) File.Delete(_dbPath);
                
                // 重新尝试打开
                _db = new LiteDatabase(_dbPath);
            }
        }
        
        /// <summary>
        /// 加载所有用户偏好设置
        /// </summary>
        private void LoadPreferences()
        {
            if (_db == null) return;
            
            // 加载处理选项
            LoadProcessingOptions();
            
            // 加载效果设置将在后续步骤实现
            Debug.WriteLine("完成加载所有用户偏好设置");
        }
        
        /// <summary>
        /// 加载处理选项
        /// </summary>
        private void LoadProcessingOptions()
        {
            if (_db == null) return;
            
            var collection = _db.GetCollection<ProcessingOptions>(PROCESSING_OPTIONS_COLLECTION);
            
            // 确保集合存在索引
            collection.EnsureIndex(x => x.Id);
            
            // 尝试获取ID为1的处理选项（我们只存储一个实例）
            var options = collection.FindById(1);
            
            // 如果存在，使用它；否则使用默认值
            if (options != null)
            {
                ProcessingOptions = options;
                Debug.WriteLine("成功加载处理选项");
            }
            else
            {
                Debug.WriteLine("未找到已保存的处理选项，使用默认值");
            }
        }
        
        /// <summary>
        /// 保存处理选项
        /// </summary>
        /// <param name="options">要保存的处理选项</param>
        public void SaveProcessingOptions(ProcessingOptions options)
        {
            if (_db == null || options == null) return;
            
            // 设置ID为1（我们只存储一个实例）
            options.Id = 1;
            
            // 获取集合并保存
            var collection = _db.GetCollection<ProcessingOptions>(PROCESSING_OPTIONS_COLLECTION);
            collection.Upsert(options);
            
            // 更新当前实例
            ProcessingOptions = options;
            
            Debug.WriteLine("成功保存处理选项");
        }
        
        /// <summary>
        /// 保存效果设置对象
        /// </summary>
        public void SaveEffectSettings<T>(T settings) where T : class
        {
            if (_db == null || settings == null) return;
            
            var collection = _db.GetCollection<T>("effectSettings");
            collection.Upsert(settings);
            
            Debug.WriteLine($"成功保存效果设置对象");
        }

        /// <summary>
        /// 获取效果设置对象
        /// </summary>
        public T? GetEffectSettings<T>(string effectTag) where T : class
        {
            if (_db == null || string.IsNullOrEmpty(effectTag)) return null;
            
            var collection = _db.GetCollection<T>("effectSettings");
            return collection.FindById(effectTag);
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _db?.Dispose();
                _disposed = true;
                Debug.WriteLine("UserPreferencesService 已释放资源");
            }
        }
    }
} 
