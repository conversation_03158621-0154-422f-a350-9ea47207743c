// ******************************************************
// 文件名: VideoEffect.cs
// 功能描述: 视频效果基类，提供所有效果卡片的通用实现
// 创建日期: 2023-12-01
// 最后修改: 2023-12-01
// 主要职责: 
//   1. 定义所有效果卡片的通用接口和属性
//   2. 提供效果启用/禁用的状态管理
//   3. 定义效果对话框和处理方法的抽象接口
// ******************************************************

using Avalonia;
using Avalonia.Controls;
using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ECutPro.Views.VideoEffectDialogs;
using ECutPro;
using ECutPro.Services;
using ECutPro.Models;

namespace ECutPro.VideoEffects
{
    /// <summary>
    /// 视频效果基类，提供所有效果卡片的通用实现
    /// </summary>
    public abstract partial class VideoEffect : ObservableObject
    {
        [ObservableProperty]
        private bool _isEnabled = false; // 是否启用

        // 添加缺失的字段
        protected EffectDialogWindow? _dialogWindow; // 对话框窗口

        // ====================== 抽象属性 ======================
        
        /// <summary>
        /// 卡片标题
        /// </summary>
        public abstract string Title { get; }
        
        /// <summary>
        /// 卡片描述
        /// </summary>
        public abstract string Description { get; }
        
        /// <summary>
        /// 卡片图标路径数据
        /// </summary>
        public abstract string IconPath { get; }
        
        /// <summary>
        /// 卡片标识
        /// </summary>
        public abstract string Tag { get; }
        
        /// <summary>
        /// 对话框宽度
        /// </summary>
        public abstract double DialogWidth { get; }
        
        /// <summary>
        /// 对话框高度
        /// </summary>
        public abstract double DialogHeight { get; }
        
        // ====================== 设置持久化方法 ======================
        
        /// <summary>
        /// 从存储加载设置 - 子类可选择重写
        /// </summary>
        public virtual void LoadSettingsFromStorage()
        {
            // 默认空实现，子类按需重写
        }
        
        /// <summary>
        /// 保存设置到存储
        /// </summary>
        public virtual void SaveSettingsToStorage()
        {
            // 子类重写实现具体逻辑
        }
        
        // ====================== 虚方法 ======================
        
        /// <summary>
        /// 显示功能对话框
        /// </summary>
        public virtual async Task<bool> ShowDialogAsync()
        {
            if (App.MainWindow == null) return false;
            
            _dialogWindow = new EffectDialogWindow
            {
                Title = $"{Title} - 参数设置"
            };
            
            // 设置对话框内容和尺寸
            _dialogWindow.SetContentAndSize(CreateDialogContent(), DialogWidth, DialogHeight);
            
            // 设置预览请求回调
            _dialogWindow.PreviewRequested += (sender, e) => OnPreview();
            
            // 使用Avalonia内置的泛型ShowDialog方法获取结果
            var result = await _dialogWindow.ShowDialog<DialogResult>(App.MainWindow);
            
            // 修改结果处理逻辑
            if (result == DialogResult.Confirmed)
            {
                IsEnabled = true;  // 启用功能
                OnConfirmed();     // 保存设置
                return true;
            }
            else if (result == DialogResult.Cancelled)
            {
                IsEnabled = false; // 禁用功能
                OnCancelled();     // 保存禁用状态
                return false;
            }
            else if (result == DialogResult.CloseOnly)
            {
                return IsEnabled;  // 保持原状态
            }
            else
            {
                return IsEnabled;
            }
        }
        
        /// <summary>
        /// 创建对话框内容（子类需重写）
        /// </summary>
        protected abstract Control CreateDialogContent();
        
        /// <summary>
        /// 预览效果（子类需重写）
        /// </summary>
        protected abstract void PreviewEffect();

        /// <summary>
        /// 创建效果参数
        /// </summary>
        public virtual EffectParam CreateEffectParam()
        {
            return new EffectParam();
        }

        /// <summary>
        /// 确认按钮点击处理
        /// </summary>
        protected virtual void OnConfirmed() 
        {
            // 保存设置到数据库
            SaveSettingsToStorage();
        }
        
        /// <summary>
        /// 取消按钮点击处理
        /// </summary>
        protected virtual void OnCancelled() 
        {
            // 保存禁用状态到数据库
            SaveSettingsToStorage();
        }
        
        /// <summary>
        /// 预览按钮点击处理
        /// </summary>
        protected virtual void OnPreview()
        {
            PreviewEffect();
        }
    }
}




