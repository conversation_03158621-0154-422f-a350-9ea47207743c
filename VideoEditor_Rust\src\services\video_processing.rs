// ******************************************************
// 视频处理服务 - 集成任务管理的纯推送架构
//
// 设计理念：
//   1. 集成任务管理 - 消除不必要的抽象层
//   2. 每个视频独立处理 - 真正的并行
//   3. 实时推送状态和进度 - 不等前端查询
//   4. 任务完成立即清理 - 零内存泄漏
//   5. 扁平化代码风格 - 清晰易读
// ******************************************************

use std::sync::Arc;
use tokio_util::sync::CancellationToken;
use uuid::Uuid;
use log::{info, error, warn};
use serde::{Deserialize, Serialize};

use crate::models::ProcessRequest;
use crate::ffmpeg::{FFmpegExecutor, FFmpegCommandBuilder};
use crate::ipc::{TcpDuplexServer, success, error};
use serde_json::json;


/// 任务状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TaskStatus {
    /// 等待中
    Pending,
    /// 处理中
    Processing,
    /// 已完成
    Completed,
    /// 失败
    Failed,
    /// 已取消
    Cancelled,
}

/// 视频处理服务 - 极简队列架构
///
/// 核心理念：
/// - 队列驱动：所有任务通过队列统一调度
/// - 全局控制：单一取消令牌控制所有FFmpeg进程
/// - 线程自治：工作线程从队列取任务，队列空时自然退出
/// - 状态推送：所有状态变化通过命名管道实时推送给前端
#[derive(Clone)]
pub struct VideoProcessingService {
    /// TCP全双工服务器 - 与前端通信的唯一通道
    tcp_server: Arc<TcpDuplexServer>,
    /// 视频队列 - 待处理视频路径的FIFO队列
    video_queue: Arc<tokio::sync::Mutex<Vec<String>>>,
    /// 全局取消令牌 - 统一控制所有FFmpeg进程的优雅停止
    global_cancellation_token: CancellationToken,
}

impl VideoProcessingService {
    /// 创建新的视频处理服务
    ///
    /// 初始化极简架构：队列、计数器、全局取消令牌
    pub fn new(tcp_server: Arc<TcpDuplexServer>) -> Self {
        Self {
            tcp_server,
            video_queue: Arc::new(tokio::sync::Mutex::new(Vec::new())),
            global_cancellation_token: CancellationToken::new(),
        }
    }
    
    /// 处理视频请求 - 极简队列模式
    pub async fn process_videos(&self, mut req: ProcessRequest) -> Result<String, String> {
        // 验证请求
        req.normalize().map_err(|e| format!("请求验证失败: {}", e))?;
        crate::utils::FrontendValidator::validate_processing_options(&req.options)
            .map_err(|e| format!("参数验证失败: {}", e))?;

        let batch_id = Uuid::new_v4().to_string();
        info!("🎬 开始批量处理，批次: {}, 视频数: {}", batch_id, req.input_paths.len());

        // 初始化队列
        *self.video_queue.lock().await = {
            let mut paths = req.input_paths.clone();
            paths.reverse(); // 反转后存储，pop()时就是正序
            paths
        };

        // 启动工作线程
        let thread_count = self.get_thread_count(&req);
        for _ in 0..thread_count {
            let service = self.clone();
            let req = req.clone();
            tokio::spawn(async move { service.worker_loop(req).await; });
        }

        info!("✅ 启动了 {} 个工作线程处理队列", thread_count);
        Ok(batch_id)
    }

    /// 取消所有任务 - 极简全局控制
    ///
    /// 核心机制：
    /// 1. 触发全局取消令牌 → 所有FFmpeg进程收到'q'命令优雅停止
    /// 2. 清空视频队列 → 工作线程发现队列空后自然退出
    /// 3. 发送全局取消消息 → 前端更新UI状态
    pub async fn cancel_all_tasks(&self) -> Result<(), String> {
        // 获取当前状态用于日志
        let queue_size = self.video_queue.lock().await.len();

        info!("🛑 开始全局取消，队列中剩余: {} 个视频", queue_size);

        // 1. 触发全局取消令牌 - 所有FFmpeg进程立即收到优雅停止信号
        self.global_cancellation_token.cancel();

        // 2. 清空队列 - 让工作线程自然退出
        self.video_queue.lock().await.clear();

        // 3. 发送全局取消消息给前端
        self.tcp_server.send_video_processing(error("用户取消处理".to_string())).await;

        info!("✅ 全局取消完成，所有FFmpeg进程将优雅停止，工作线程将自然退出");
        Ok(())
    }

    /// 获取线程数配置 - 智能调度 + 手动选择
    fn get_thread_count(&self, req: &ProcessRequest) -> usize {
        let base_threads = match req.options.thread_count_index {
            0 => return self.get_smart_thread_count(req), // 智能调度
            1 => 1,   // 单线程
            2 => 2,   // 2线程
            3 => 4,   // 4线程
            4 => 8,   // 8线程
            _ => return self.get_smart_thread_count(req), // 默认智能调度
        };

        base_threads
    }

    /// 智能线程数计算 - 根据加速模式、文件数量和系统资源自动优化
    fn get_smart_thread_count(&self, req: &ProcessRequest) -> usize {
        let file_count = req.input_paths.len();
        let cpu_cores = num_cpus::get();
        let is_gpu_enabled = req.options.gpu_acceleration_index > 0;

        let optimal_threads = if is_gpu_enabled {
            // GPU加速模式：限制并发数，避免GPU编码器竞争
            file_count.min(2).max(1)  // 最多2个并发任务，至少1个
        } else {
            // CPU模式：根据CPU能力优化并发数
            if file_count <= 2 {
                // 少量文件：使用文件数量
                file_count
            } else if file_count <= cpu_cores {
                // 中等文件数：使用文件数量，但不超过CPU核心数
                file_count
            } else {
                // 大量文件：使用CPU核心数的1.5倍（利用超线程）
                (cpu_cores * 3 / 2).max(1)
            }
        }.max(1); // 至少1个线程

        info!("🧠 智能调度: {}个文件 → {}个并发任务 (模式: {}, CPU核心: {})",
              file_count, optimal_threads,
              if is_gpu_enabled { "GPU加速" } else { "CPU编码" },
              cpu_cores);

        optimal_threads
    }

    /// 工作线程主循环 - 极简设计
    async fn worker_loop(&self, req: ProcessRequest) {
        loop {
            // 从队列获取下一个视频
            let input_path = match self.video_queue.lock().await.pop() {
                Some(path) => path,
                None => break, // 队列空了，退出
            };

            info!("🎬 开始处理视频: {}", input_path);

            // 直接处理视频
            let is_success = self.process_single_video(&input_path, &req).await;

            // 检查全局取消状态
            if self.global_cancellation_token.is_cancelled() {
                return; // 被全局取消，直接返回
            }

            // 推送结果状态 - 只推送单个视频状态，让前端计算全局进度
            if is_success {
                self.tcp_server.send_video_processing(success(json!({
                    "task_id": input_path,
                    "progress": "处理完成"
                }))).await;
                info!("✅ 视频处理完成: {}", input_path);
            } else {
                self.tcp_server.send_video_processing(error("处理失败".to_string())).await;
                error!("❌ 视频处理失败: {}", input_path);
            }
        }
    }

    /// 处理单个视频 - 带自动文件清理
    async fn process_single_video(&self, input_path: &str, req: &ProcessRequest) -> bool {
        // 构建FFmpeg命令
        let cmd_builder = match FFmpegCommandBuilder::new(
            input_path,
            &req.output_dir,
            &req.options,
            &req.effects,
            0
        ) {
            Ok(builder) => builder,
            Err(e) => {
                let error_msg = format!("FFmpeg命令构建失败: {}", e);
                self.tcp_server.send_video_processing(error(error_msg.clone())).await;
                error!("❌ 视频处理失败: {} - {}", input_path, error_msg);
                return false;
            }
        };

        let cmd = cmd_builder.build();
        let output_path = cmd_builder.output_path();

        // 创建临时文件守护者 - 确保异常时也能清理文件
        let mut temp_guard = TempFileGuard::new(&output_path);

        // 创建进度回调 - 只推送单个视频进度
        let service = self.clone();
        let task_id = input_path.to_string();
        let progress_callback = move |progress: f32| {
            let service = service.clone();
            let task_id = task_id.clone();
            tokio::spawn(async move {
                service.tcp_server.send_video_processing(success(json!({
                    "task_id": task_id,
                    "progress": format!("处理中 ({:.0}%)", progress)
                }))).await;
            });
        };

        // 执行FFmpeg命令
        let result = FFmpegExecutor::execute(&cmd, progress_callback, Some(self.global_cancellation_token.clone())).await;

        // 处理结果 - 使用卫语句减少嵌套

        // 卫语句1：FFmpeg执行失败
        let Ok(_) = result else {
            error!("❌ 视频处理失败: {} - {}", input_path, result.unwrap_err());
            return false; // 守护者自动清理文件
        };

        // 卫语句2：全局取消
        if self.global_cancellation_token.is_cancelled() {
            return false; // 守护者自动清理文件
        }

        // 成功路径：保留文件并处理原文件
        temp_guard.keep();

        if req.options.delete_original_index == 1 {
            let _ = std::fs::remove_file(input_path);
        }

        true
    }
}

/// 临时文件守护者 - RAII自动清理机制
///
/// 默认在Drop时删除文件，调用`keep()`可保留文件
struct TempFileGuard {
    path: std::path::PathBuf,
    should_cleanup: bool,
}

impl TempFileGuard {
    /// 创建临时文件守护者
    fn new(path: impl Into<std::path::PathBuf>) -> Self {
        Self {
            path: path.into(),
            should_cleanup: true,
        }
    }

    /// 保留文件，取消自动清理
    fn keep(&mut self) {
        self.should_cleanup = false;
    }
}

impl Drop for TempFileGuard {
    fn drop(&mut self) {
        // 卫语句1：不需要清理
        if !self.should_cleanup {
            return;
        }

        // 卫语句2：删除成功
        if std::fs::remove_file(&self.path).is_ok() {
            info!("🧹 自动清理临时文件: {:?}", self.path);
            return;
        }

        // 卫语句3：文件不存在（正常情况）
        if !self.path.exists() {
            return;
        }

        // 到这里说明删除失败且文件存在
        warn!("⚠️ 清理临时文件失败: {:?}", self.path);
    }
}


