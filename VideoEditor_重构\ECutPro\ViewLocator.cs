using System;
using Avalonia.Controls;
using Avalonia.Controls.Templates;
using ECutPro.ViewModels;
using ECutPro.Views;

namespace ECutPro;

public class ViewLocator : IDataTemplate
{
    public Control? Build(object? param)
    {
        if (param is null)
            return null;
        
        // 使用显式类型检查而不是反射
        if (param is MainWindowViewModel)
            return new MainWindow();
            
        // 完全避免反射
        return new TextBlock { Text = "未找到匹配的视图" };
    }

    public bool Match(object? data)
    {
        return data is MainWindowViewModel;
    }
}
