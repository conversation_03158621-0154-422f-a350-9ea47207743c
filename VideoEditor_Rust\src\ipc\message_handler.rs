// ******************************************************
// 消息处理器 - 纯推送架构
// 
// 设计理念：
//   1. 极简API - 只有处理和取消两个核心功能
//   2. 实时推送 - 不提供查询接口
//   3. 全局取消 - 用户体验优先
//   4. 扁平化处理 - 减少嵌套
// ******************************************************

use std::sync::Arc;
use serde::{Deserialize, Serialize};
use log::{info, error};

use crate::services::video_processing::VideoProcessingService;
use crate::services::video_analysis::VideoAnalysisService;
use crate::services::video_preview::VideoPreviewService;
use crate::models::{ProcessRequest, EffectParam};
use crate::ipc::TcpDuplexServer;

/// 命令类型枚举 - 简化版
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum Command {
    /// 处理视频命令
    ProcessVideos(ProcessRequest),
    
    /// 取消所有任务命令（简化：不需要指定任务ID）
    CancelAllTasks,
    
    /// 分析视频命令
    AnalyzeVideo(String),
    
    /// 预览效果命令
    PreviewEffect {
        /// 视频路径
        video_path: String,
        /// 效果参数
        effect: EffectParam,
    },
    
    /// 检查GPU加速命令（GPU类型：amd、nvidia、intel）
    CheckGpuAcceleration(String),
}

/// 消息处理器 - 纯推送架构
#[derive(Clone)]
pub struct MessageHandler {
    /// 视频处理服务
    video_service: Arc<VideoProcessingService>,
    /// 视频分析服务
    analysis_service: VideoAnalysisService,
    /// 视频预览服务
    preview_service: VideoPreviewService,
    /// TCP全双工服务器
    tcp_server: Arc<TcpDuplexServer>,
}

impl MessageHandler {
    /// 创建新的消息处理器
    pub fn new(
        video_service: Arc<VideoProcessingService>,
        analysis_service: VideoAnalysisService,
        preview_service: VideoPreviewService,
        tcp_server: Arc<TcpDuplexServer>,
    ) -> Self {
        Self {
            video_service,
            analysis_service,
            preview_service,
            tcp_server,
        }
    }
    
    /// 处理命令 - 扁平化风格
    pub async fn handle_command(&self, command: Command) {
        match command {
            Command::ProcessVideos(req) => {
                self.handle_process_videos(req).await;
            }
            
            Command::CancelAllTasks => {
                self.handle_cancel_all_tasks().await;
            }
            
            Command::AnalyzeVideo(video_path) => {
                self.handle_analyze_video(video_path).await;
            }
            
            Command::PreviewEffect { video_path, effect } => {
                self.handle_preview_effect(video_path, effect).await;
            }
            
            Command::CheckGpuAcceleration(gpu_type) => {
                self.handle_check_gpu_acceleration(gpu_type).await;
            }
        }
    }
    
    /// 处理视频处理命令
    async fn handle_process_videos(&self, req: ProcessRequest) {
        info!("🎬 收到处理视频命令，视频数量: {}", req.input_paths.len());
        
        match self.video_service.process_videos(req).await {
            Ok(batch_id) => {
                info!("✅ 视频处理任务启动成功，批次: {}", batch_id);
                // 不需要发送响应，所有状态都通过推送发送
            }
            Err(error) => {
                error!("❌ 视频处理任务启动失败: {}", error);
                // 发送全局错误消息
                self.tcp_server.send_video_processing(crate::ipc::error(error)).await;
            }
        }
    }
    
    /// 处理取消所有任务命令
    async fn handle_cancel_all_tasks(&self) {
        info!("🛑 收到取消所有任务命令");
        
        match self.video_service.cancel_all_tasks().await {
            Ok(_) => {
                info!("✅ 所有任务取消成功");
                // 不需要发送响应，取消状态通过推送发送
            }
            Err(error) => {
                error!("❌ 取消任务失败: {}", error);
                self.tcp_server.send_video_processing(crate::ipc::error(error)).await;
            }
        }
    }
    
    /// 处理视频分析命令 - 纯转发
    async fn handle_analyze_video(&self, video_path: String) {
        info!("🔍 收到分析视频命令: {}", video_path);

        // 直接转发给分析服务，结果由服务自己推送
        self.analysis_service.analyze_video(&video_path).await;
    }
    
    /// 处理预览效果命令 - 纯转发
    async fn handle_preview_effect(&self, video_path: String, effect: EffectParam) {
        info!("👁️ 收到预览效果命令: {}", video_path);

        // 直接转发给预览服务，结果由服务自己推送
        self.preview_service.preview_effect(&video_path, &effect).await;
    }
    
    /// 处理GPU加速检查命令 - 纯转发
    async fn handle_check_gpu_acceleration(&self, gpu_type: String) {
        info!("🚀 收到GPU加速检查命令: {}", gpu_type);

        // 直接转发给分析服务，结果由服务自己推送
        self.analysis_service.check_gpu_acceleration(&gpu_type).await;
    }
}
