// ******************************************************
// 文件名: App.axaml.cs
// 功能描述: 应用程序入口类
// 创建日期: 2023-12-01
// 最后修改: 2023-12-01
// 主要职责: 
//   1. 管理应用程序服务实例
//   2. 初始化应用程序框架
//   3. 提供全局服务访问
// ******************************************************

using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Data.Core;
using Avalonia.Data.Core.Plugins;
using Avalonia.Layout;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Styling;
using Avalonia.VisualTree;
using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ECutPro.Services;
using ECutPro.ViewModels;
using ECutPro.Views;
using System.Collections.ObjectModel;

namespace ECutPro;

/// <summary>
/// 应用程序主类
/// </summary>
public partial class App : Application
{
    /// <summary>
    /// 应用程序实例访问点
    /// </summary>
    public new static App Current => (App)Application.Current!;
    
    /// <summary>
    /// 应用程序主窗口
    /// </summary>
    public static Window? MainWindow { get; private set; }
    
    /// <summary>
    /// 主窗口视图模型
    /// </summary>
    public static MainWindowViewModel? MainViewModel { get; private set; }
    
    /// <summary>
    /// 视频文件管理服务
    /// </summary>
    public static VideoFileManager FileManager { get; private set; } = null!;

    /// <summary>
    /// 文件对话框服务
    /// </summary>
    public static FileDialogService DialogService { get; private set; } = null!;

    /// <summary>
    /// TCP视频服务
    /// </summary>
    public static TcpVideoService TcpService { get; private set; } = null!;

    /// <summary>
    /// 用户偏好设置服务
    /// </summary>
    public static UserPreferencesService UserPreferences { get; private set; } = null!;

    /// <summary>
    /// 当前显示的提示窗口
    /// </summary>
    private static Window? _currentToast;

    /// <summary>
    /// 初始化应用程序
    /// </summary>
    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
        
        // 显式创建服务实例
        InitializeServices();
    }

    /// <summary>
    /// 初始化服务实例 - 最简洁的静态属性方案
    /// </summary>
    private void InitializeServices()
    {
        System.Diagnostics.Debug.WriteLine("🔧 开始初始化服务...");

        // 核心服务 - 简洁的直接初始化
        UserPreferences = new UserPreferencesService();
        DialogService = new FileDialogService();
        FileManager = new VideoFileManager();
        TcpService = new TcpVideoService();

        // 创建主视图模型
        MainViewModel = new MainWindowViewModel();

        System.Diagnostics.Debug.WriteLine("🔧 服务初始化完成，开始连接后端...");

        // 异步连接到后端服务
        _ = Task.Run(async () =>
        {
            System.Diagnostics.Debug.WriteLine("🔗 开始尝试连接后端服务...");
            try
            {
                // 添加5秒超时
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                await TcpService.ConnectAsync(cts.Token);
                System.Diagnostics.Debug.WriteLine("✅ 成功连接到后端服务");
            }
            catch (OperationCanceledException)
            {
                System.Diagnostics.Debug.WriteLine("⏰ 连接超时，后端可能未启动");
                // 在UI线程显示错误提示
                Avalonia.Threading.Dispatcher.UIThread.Post(() =>
                {
                    ShowToast("连接后端超时，请确保后端已启动");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 连接后端服务失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ 异常类型: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"❌ 堆栈跟踪: {ex.StackTrace}");
                // 在UI线程显示错误提示
                Avalonia.Threading.Dispatcher.UIThread.Post(() =>
                {
                    ShowToast($"连接后端服务失败: {ex.Message}");
                });
            }
        });
    }



    /// <summary>
    /// 框架初始化完成后的处理
    /// </summary>
    public override void OnFrameworkInitializationCompleted()
    {
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            // 避免重复验证
            DisableAvaloniaDataAnnotationValidation();
            
            // 创建主窗口并设置数据上下文
            desktop.MainWindow = new MainWindow();
            
            // 存储主窗口引用
            MainWindow = desktop.MainWindow;
            
            // 注册退出事件
            desktop.ShutdownRequested += OnShutdownRequested;
        }
        base.OnFrameworkInitializationCompleted();
    }

    /// <summary>
    /// 禁用Avalonia数据注解验证
    /// </summary>
    private void DisableAvaloniaDataAnnotationValidation()
    {
        // 添加条件检查，避免AOT警告
        if (BindingPlugins.DataValidators != null)
        {
            // 获取要删除的插件数组
            var dataValidationPluginsToRemove =
                BindingPlugins.DataValidators.OfType<DataAnnotationsValidationPlugin>().ToArray();

            // 删除每个找到的条目
            foreach (var plugin in dataValidationPluginsToRemove)
            {
                BindingPlugins.DataValidators.Remove(plugin);
            }
        }
    }
    


    /// <summary>
    /// 显示Toast提示信息，两秒后自动消失
    /// </summary>
    /// <param name="message">消息内容</param>
    public static void ShowToast(string message)
    {
        // 确保UI线程上执行
        Avalonia.Threading.Dispatcher.UIThread.Post(async () =>
        {
            // 如果已有正在显示的提示窗口，先关闭它
            if (_currentToast != null)
            {
                _currentToast.Close();
                _currentToast = null;
            }
            
            // 创建Toast窗口 - 极简实现
            _currentToast = new Window
            {
                // 最小必要的窗口属性
                Background = new SolidColorBrush(Colors.Transparent), // 透明背景
                SystemDecorations = SystemDecorations.None, // 无边框
                WindowStartupLocation = WindowStartupLocation.CenterOwner, // 居中显示
                ShowInTaskbar = false, // 不显示在任务栏
                Width = MainWindow?.Width ?? 1200,
                Height = MainWindow?.Height ?? 800,
                
                // 设置窗口内容 - 简化为单个遮罩
                Content = new Grid
                {
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    VerticalAlignment = VerticalAlignment.Stretch,
                    Children =
                    {
                        new Border
                        {
                            Background = new SolidColorBrush(Color.Parse("#60000000")), // 半透明黑色遮罩
                            CornerRadius = new CornerRadius(12), // 圆角
                            Padding = new Thickness(20, 10), // 添加内边距，确保文本有足够空间
                            HorizontalAlignment = HorizontalAlignment.Center,
                            VerticalAlignment = VerticalAlignment.Center,
                            Child = new TextBlock
                            {
                                Text = message,
                                TextAlignment = TextAlignment.Center,
                                HorizontalAlignment = HorizontalAlignment.Center,
                                VerticalAlignment = VerticalAlignment.Center,
                                Foreground = new SolidColorBrush(Colors.White),
                                FontSize = 14,
                                TextWrapping = TextWrapping.Wrap, // 允许文本换行
                                MaxWidth = 500 // 设置最大宽度，避免过长的消息
                            }
                        }
                    }
                }
            };
            
            // 显示Toast窗口
            _currentToast.Show(MainWindow ?? null);
            
            // 等待1秒后关闭Toast
            await Task.Delay(1000);
            
            // 关闭Toast窗口
            if (_currentToast != null)
            {
                _currentToast.Close();
                _currentToast = null;
            }
        });
    }

    /// <summary>
    /// 应用程序关闭时清理资源
    /// </summary>
    private void OnShutdownRequested(object? sender, ShutdownRequestedEventArgs e)
    {
        // 检查是否有正在进行的任务并取消
        if (MainViewModel?.IsProcessing == true)
        {
            try
            {
                // 使用TcpService取消任务
                TcpService.CancelAllTasksAsync().GetAwaiter().GetResult();
                System.Diagnostics.Debug.WriteLine("应用程序关闭时取消了正在进行的处理任务");
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止关闭
                System.Diagnostics.Debug.WriteLine($"取消任务时出错: {ex.Message}");
            }
        }

        // 清理服务资源
        UserPreferences?.Dispose();
        TcpService?.Dispose();
        MainViewModel = null;
        UserPreferences = null;
    }
}


