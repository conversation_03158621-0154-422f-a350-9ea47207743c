using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using Avalonia.Controls.Primitives;
using System;
using System.IO;
using Avalonia.Media.Imaging;

namespace ECutPro.Views.VideoEffectDialogs
{
    /// <summary>
    /// 对话框结果枚举
    /// </summary>
    public enum DialogResult
    {
        /// <summary>
        /// 确认/启用功能
        /// </summary>
        Confirmed,
        
        /// <summary>
        /// 取消/关闭功能
        /// </summary>
        Cancelled,
        
        /// <summary>
        /// 预览
        /// </summary>
        Preview,
        
        /// <summary>
        /// 仅关闭窗口，不改变功能状态
        /// </summary>
        CloseOnly
    }
        
    /// <summary>
    /// 视频效果对话框窗口
    /// </summary>
    public partial class EffectDialogWindow : Window
    {
        /// <summary>
        /// 预览请求事件
        /// </summary>
        public event EventHandler? PreviewRequested;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public EffectDialogWindow()
        {
            InitializeComponent();
            DataContext = this;
            
            // 动态获取主窗口尺寸并设置当前窗口尺寸
            this.Width = App.MainWindow?.Width ?? 1200;
            this.Height = App.MainWindow?.Height ?? 800;
            
            // 绑定启用按钮事件
            var confirmButton = this.FindControl<Button>("ConfirmButton");
            if (confirmButton != null)
            {
                confirmButton.Click += (s, e) => Close(DialogResult.Confirmed);
            }
            // 绑定取消按钮事件
            var cancelButton = this.FindControl<Button>("CancelButton");
            if (cancelButton != null)
            {
                cancelButton.Click += (s, e) => Close(DialogResult.Cancelled);
            }
            // 绑定关闭按钮事件
            var closeButton = this.FindControl<Button>("CloseButton");
            if (closeButton != null)
            {
                closeButton.Click += (s, e) => Close(DialogResult.CloseOnly);
            }
            // 绑定预览按钮事件
            var previewButton = this.FindControl<Button>("PreviewButton");
            if (previewButton != null)
            {
                previewButton.Click += (s, e) => PreviewRequested?.Invoke(this, EventArgs.Empty);
            }
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }
        
        /// <summary>
        /// 设置对话框内容和尺寸
        /// </summary>
        public void SetContentAndSize(Control content, double width = 500, double height = 350)
        {
            var contentArea = this.FindControl<ContentControl>("ContentArea");
            if (contentArea != null)
            {
                contentArea.Content = content;
            }
            
            // 设置对话框尺寸
            var dialogBorder = this.FindControl<Border>("DialogBorder");
            if (dialogBorder != null)
            {
                dialogBorder.Width = width;
                dialogBorder.Height = height;
            }
        }
        
        /// <summary>
        /// 显示预览窗口
        /// </summary>
        /// <param name="tempImagePath">临时预览图像路径</param>
        public void ShowPreviewWindow(string tempImagePath)
        {

            if (string.IsNullOrEmpty(tempImagePath) || !File.Exists(tempImagePath)) {
                App.ShowToast("预览窗口加载失败，请检查文件是否存在");
                return;
            }
            
            try
            {
                // 创建简单的预览窗口
                var previewWindow = new Window
                {
                    Title = $"{this.Title} - 预览",
                    Width = 800,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    SystemDecorations = SystemDecorations.Full,
                    CanResize = true,
                    Content = new Viewbox
                    {
                        Stretch = Avalonia.Media.Stretch.Uniform,
                        Child = new Image { Source = new Bitmap(tempImagePath) }
                    }
                };
                
                // 窗口关闭时删除临时文件
                previewWindow.Closed += (s, e) =>
                {
                    if (File.Exists(tempImagePath)) File.Delete(tempImagePath);
                };
                
                // 显示预览窗口
                previewWindow.Show(this);
            }
            catch
            {
                if (File.Exists(tempImagePath)) File.Delete(tempImagePath);
                App.ShowToast("预览出错，请重试");
            }
        }
    }
} 