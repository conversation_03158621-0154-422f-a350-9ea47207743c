// ******************************************************
// 文件名: FrameAnimationControl.cs
// 功能描述: 序列帧动画控件
// 创建日期: 2025-07-21
// 最后修改: 2025-07-21
// 主要职责: 
//   1. 加载并播放序列帧动画
//   2. 支持各种播放控制（开始、暂停、重启）
//   3. 兼容不同来源的帧（包括视频提取帧）
// ******************************************************

using Avalonia;
using Avalonia.Controls;
using Avalonia.Media;
using Avalonia.Media.Imaging;
using Avalonia.Threading;
using Avalonia.VisualTree;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Diagnostics;
using System.Threading.Tasks; // Added for Task.Delay
using System.Reflection; // 添加引用，用于读取嵌入资源

namespace ECutPro.Controls
{
    /// <summary>
    /// 一个播放序列帧动画的控件
    /// </summary>
    /// <remarks>
    /// 使用示例：
    /// <code>
    /// <!-- XAML中使用 -->
    /// <controls:FrameAnimationControl/>
    /// 
    /// // 代码中控制
    /// frameAnimationControl.Play(60.0); // 以60fps开始播放默认加载动画
    /// frameAnimationControl.Play(30.0, "D:/VideoFrames"); // 播放指定路径的帧序列
    /// frameAnimationControl.Stop();     // 停止播放并释放资源
    /// </code>
    /// </remarks>
    public class FrameAnimationControl : Control
    {
        // 静态属性定义
        public static readonly DirectProperty<FrameAnimationControl, bool> IsPlayingProperty =
            AvaloniaProperty.RegisterDirect<FrameAnimationControl, bool>(
                nameof(IsPlaying),
                o => o.IsPlaying);

        private bool _isPlaying;
        /// <summary>
        /// 指示动画当前是否正在播放
        /// </summary>
        public bool IsPlaying
        {
            get => _isPlaying;
            private set => SetAndRaise(IsPlayingProperty, ref _isPlaying, value);
        }
        
        // 私有字段
        /// <summary>
        /// 加载的位图帧集合
        /// </summary>
        private readonly List<Bitmap> _frames = new();

        /// <summary>
        /// 当前显示的帧索引
        /// </summary>
        private int _currentFrameIndex;

        /// <summary>
        /// 帧更新定时器
        /// </summary>
        private readonly DispatcherTimer _timer;

        // 用于保持播放速度一致的字段
        private DateTime _lastFrameTime;
        private double _framePosition;
        private double _baseAnimationRate = 30.0; // 默认基准帧率

        /// <summary>
        /// 构造函数
        /// </summary>
        public FrameAnimationControl()
        {
            // 创建定时器但不启动
            _timer = new DispatcherTimer();
            
            // 注册帧更新事件
            _timer.Tick += OnTimerTick;
        }

        /// <summary>
        /// 根据传入的帧率设置定时器间隔
        /// </summary>
        /// <param name="frameRate">要设置的帧率</param>
        private void UpdateTimerInterval(double frameRate)
        {
            // 确保帧率至少为1fps
            double fps = Math.Max(1.0, frameRate);
            
            // 设置定时器间隔 - 只影响渲染流畅度
            double intervalSeconds = 1.0 / fps;
            _timer.Interval = TimeSpan.FromSeconds(intervalSeconds);
            
            // 同时设置基准帧率 - 使播放速度与原始帧率匹配
            _baseAnimationRate = Math.Max(30.0, fps);
            
            Debug.WriteLine($"帧率设置为: {fps}fps (间隔: {intervalSeconds * 1000:F2}ms)");
        }

        /// <summary>
        /// 控件附加到视觉树时调用（为空实现，仅调用基类方法）
        /// </summary>
        protected override void OnAttachedToVisualTree(VisualTreeAttachmentEventArgs e)
        {
            base.OnAttachedToVisualTree(e);
            // 不执行任何自动播放，只有通过Play方法显式调用才会播放
        }

        /// <summary>
        /// 控件从视觉树分离时调用
        /// </summary>
        protected override void OnDetachedFromVisualTree(VisualTreeAttachmentEventArgs e)
        {
            base.OnDetachedFromVisualTree(e);
            // 强制停止并清理资源
            Stop();
        }

        /// <summary>
        /// 加载帧序列
        /// </summary>
        private void LoadFrames(string framesPath = null)
        {
            ClearFrames();

            // 如果路径为空，则使用默认加载动画路径
            string pathToUse = string.IsNullOrWhiteSpace(framesPath) 
                ? "ECutPro.Assets.loading"  // 默认嵌入资源路径标识符
                : framesPath;       // 用户设置的路径
            
            // 判断是否是嵌入资源路径
            if (pathToUse == "ECutPro.Assets.loading")
            {
                var assembly = Assembly.GetExecutingAssembly();
                var resourceNames = assembly.GetManifestResourceNames()
                    .Where(name => name.StartsWith(pathToUse))
                    .OrderBy(name => int.Parse(Path.GetFileNameWithoutExtension(name.Substring(pathToUse.Length+1))))
                    .ToList();
                
                foreach (var name in resourceNames)
                {
                    using var stream = assembly.GetManifestResourceStream(name);
                    _frames.Add(new Bitmap(stream));
                }
                
                Debug.WriteLine($"从嵌入资源加载了 {_frames.Count} 个帧");
                return;
            }
            
            // 从文件系统加载
            string fullPath = Path.GetFullPath(pathToUse);
            
            if (!Directory.Exists(fullPath))
            {
                // 尝试在应用程序目录查找
                fullPath = Path.Combine(AppContext.BaseDirectory, pathToUse);
                
                if (!Directory.Exists(fullPath))
                {
                    Debug.WriteLine($"无法找到帧目录: {pathToUse}");
                    return;
                }
            }

            var files = Directory.GetFiles(fullPath, "*.png")
                .OrderBy(f => NaturalSort(Path.GetFileName(f)))
                .ToList();
            
            if (files.Count == 0)
            {
                Debug.WriteLine($"目录 {fullPath} 中没有PNG文件");
                return;
            }

            // 加载所有帧
            foreach (var file in files)
            {
                var bitmap = new Bitmap(file);
                _frames.Add(bitmap);
            }
            
            Debug.WriteLine($"从 {fullPath} 加载了 {_frames.Count} 个帧");
        }

        /// <summary>
        /// 清理帧资源
        /// </summary>
        private void ClearFrames()
        {
            foreach (var frame in _frames)
                frame.Dispose();
            
            _frames.Clear();
            _currentFrameIndex = 0;
            
            // 同时重置播放状态变量
            _lastFrameTime = default;
            _framePosition = 0.0;
        }

        /// <summary>
        /// 渲染当前帧
        /// </summary>
        public override void Render(DrawingContext context)
        {
            base.Render(context);

            if (_frames.Count == 0 || _currentFrameIndex >= _frames.Count)
                return;

            var frame = _frames[_currentFrameIndex];
            
            // 计算绘制区域以适应控件大小
            var sourceSize = frame.Size;
            var destSize = Bounds.Size;
            
            // 保持宽高比
            double scale = Math.Min(destSize.Width / sourceSize.Width, destSize.Height / sourceSize.Height);
            double width = sourceSize.Width * scale;
            double height = sourceSize.Height * scale;
            
            // 居中显示
            double x = (destSize.Width - width) / 2;
            double y = (destSize.Height - height) / 2;
            
            Rect destRect = new(x, y, width, height);
            
            // 绘制当前帧
            context.DrawImage(frame, new Rect(0, 0, sourceSize.Width, sourceSize.Height), destRect);
        }

        /// <summary>
        /// 帧更新事件处理
        /// </summary>
        private void OnTimerTick(object sender, EventArgs e)
        {
            if (_frames.Count == 0)
                return;
            
            // 当前时间
            DateTime now = DateTime.Now;
            
            // 如果是第一次调用，初始化状态
            if (_lastFrameTime == default)
            {
                _lastFrameTime = now;
                _framePosition = 0.0;
            }
            
            // 计算经过的时间
            double elapsedSeconds = (now - _lastFrameTime).TotalSeconds;
            
            // 更新帧位置（基于设置的帧率）
            _framePosition += elapsedSeconds * _baseAnimationRate;
            
            // 计算当前帧索引
            _currentFrameIndex = (int)_framePosition % _frames.Count;
            
            // 保存当前时间以供下次使用
            _lastFrameTime = now;
            
            // 更新显示
            InvalidateVisual();
        }

        /// <summary>
        /// 开始播放动画，设置帧率
        /// </summary>
        /// <param name="frameRate">动画帧率</param>
        /// <param name="framesPath">帧序列路径，如果为null则使用默认加载动画</param>
        public void Play(double frameRate, string framesPath = null)
        {
            // 设置定时器间隔（只影响渲染流畅度，不影响播放速度）
            UpdateTimerInterval(frameRate);
            
            // 加载帧资源
            LoadFrames(framesPath);
            
            // 开始播放
            if (_frames.Count > 0)
            {
                _timer.Start();
                IsPlaying = true;
                Debug.WriteLine($"开始播放动画，帧率: {frameRate}fps");
            }
        }
        
        /// <summary>
        /// 停止播放动画并释放所有资源
        /// </summary>
        public void Stop()
        {
            if (IsPlaying)
            {
                _timer.Stop();
                IsPlaying = false;
                Debug.WriteLine("停止播放动画");
            }
            
            // 释放所有资源并重置状态
            ClearFrames();
            
            // 刷新UI确保视图更新
            InvalidateVisual();
            
            Debug.WriteLine("释放动画帧资源");
        }
        
        /// <summary>
        /// 自然排序辅助方法
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>用于自然排序的转换后字符串</returns>
        private static string NaturalSort(string input) => 
            System.Text.RegularExpressions.Regex.Replace(
                input,
                "[0-9]+",
                match => match.Value.PadLeft(10, '0')
            );
    }
} 