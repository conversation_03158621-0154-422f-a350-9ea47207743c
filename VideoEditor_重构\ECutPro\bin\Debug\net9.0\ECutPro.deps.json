{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"ECutPro/1.0.0": {"dependencies": {"Avalonia": "11.3.2", "Avalonia.Desktop": "11.3.2", "Avalonia.Diagnostics": "11.3.2", "Avalonia.Fonts.Inter": "11.3.2", "Avalonia.Themes.Fluent": "11.3.2", "CommunityToolkit.Mvvm": "8.4.0", "LiteDB": "5.0.21", "Microsoft.DotNet.ILCompiler": "9.0.7", "Microsoft.NET.ILLink.Tasks": "9.0.7"}, "runtime": {"ECutPro.dll": {}}}, "Avalonia/11.3.2": {"dependencies": {"Avalonia.BuildServices": "0.0.31", "Avalonia.Remote.Protocol": "11.3.2", "MicroCom.Runtime": "0.11.0"}, "runtime": {"lib/net8.0/Avalonia.Base.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}, "lib/net8.0/Avalonia.Controls.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}, "lib/net8.0/Avalonia.DesignerSupport.dll": {"assemblyVersion": "0.7.0.0", "fileVersion": "0.7.0.0"}, "lib/net8.0/Avalonia.Dialogs.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}, "lib/net8.0/Avalonia.Markup.Xaml.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}, "lib/net8.0/Avalonia.Markup.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}, "lib/net8.0/Avalonia.Metal.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}, "lib/net8.0/Avalonia.MicroCom.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}, "lib/net8.0/Avalonia.OpenGL.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}, "lib/net8.0/Avalonia.Vulkan.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}, "lib/net8.0/Avalonia.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.Angle.Windows.Natives/2.1.25547.20250602": {"runtimeTargets": {"runtimes/win-arm64/native/av_libglesv2.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "2.1.25606.0"}, "runtimes/win-x64/native/av_libglesv2.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.1.25606.0"}, "runtimes/win-x86/native/av_libglesv2.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2.1.25606.0"}}}, "Avalonia.BuildServices/0.0.31": {}, "Avalonia.Controls.ColorPicker/11.3.2": {"dependencies": {"Avalonia": "11.3.2", "Avalonia.Remote.Protocol": "11.3.2"}, "runtime": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.Desktop/11.3.2": {"dependencies": {"Avalonia": "11.3.2", "Avalonia.Native": "11.3.2", "Avalonia.Skia": "11.3.2", "Avalonia.Win32": "11.3.2", "Avalonia.X11": "11.3.2"}, "runtime": {"lib/net8.0/Avalonia.Desktop.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.Diagnostics/11.3.2": {"dependencies": {"Avalonia": "11.3.2", "Avalonia.Controls.ColorPicker": "11.3.2", "Avalonia.Themes.Simple": "11.3.2"}, "runtime": {"lib/net8.0/Avalonia.Diagnostics.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.Fonts.Inter/11.3.2": {"dependencies": {"Avalonia": "11.3.2"}, "runtime": {"lib/net8.0/Avalonia.Fonts.Inter.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.FreeDesktop/11.3.2": {"dependencies": {"Avalonia": "11.3.2", "Tmds.DBus.Protocol": "0.21.2"}, "runtime": {"lib/net8.0/Avalonia.FreeDesktop.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.Native/11.3.2": {"dependencies": {"Avalonia": "11.3.2"}, "runtime": {"lib/net8.0/Avalonia.Native.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}, "runtimeTargets": {"runtimes/osx/native/libAvaloniaNative.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Avalonia.Remote.Protocol/11.3.2": {"runtime": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.Skia/11.3.2": {"dependencies": {"Avalonia": "11.3.2", "HarfBuzzSharp": "8.3.1.1", "HarfBuzzSharp.NativeAssets.Linux": "8.3.1.1", "HarfBuzzSharp.NativeAssets.WebAssembly": "8.3.1.1", "SkiaSharp": "2.88.9", "SkiaSharp.NativeAssets.Linux": "2.88.9", "SkiaSharp.NativeAssets.WebAssembly": "2.88.9"}, "runtime": {"lib/net8.0/Avalonia.Skia.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.Themes.Fluent/11.3.2": {"dependencies": {"Avalonia": "11.3.2"}, "runtime": {"lib/net8.0/Avalonia.Themes.Fluent.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.Themes.Simple/11.3.2": {"dependencies": {"Avalonia": "11.3.2"}, "runtime": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.Win32/11.3.2": {"dependencies": {"Avalonia": "11.3.2", "Avalonia.Angle.Windows.Natives": "2.1.25547.20250602"}, "runtime": {"lib/net8.0/Avalonia.Win32.Automation.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}, "lib/net8.0/Avalonia.Win32.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "Avalonia.X11/11.3.2": {"dependencies": {"Avalonia": "11.3.2", "Avalonia.FreeDesktop": "11.3.2", "Avalonia.Skia": "11.3.2"}, "runtime": {"lib/net8.0/Avalonia.X11.dll": {"assemblyVersion": "11.3.2.0", "fileVersion": "11.3.2.0"}}}, "CommunityToolkit.Mvvm/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.1"}}}, "HarfBuzzSharp/8.3.1.1": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "8.3.1.1", "HarfBuzzSharp.NativeAssets.macOS": "8.3.1.1"}, "runtime": {"lib/net8.0/HarfBuzzSharp.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "8.3.1.1"}}}, "HarfBuzzSharp.NativeAssets.Linux/8.3.1.1": {"runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-loongarch64/native/libHarfBuzzSharp.so": {"rid": "linux-loongarch64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libHarfBuzzSharp.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-loongarch64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-loongarch64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-riscv64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-riscv64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-riscv64/native/libHarfBuzzSharp.so": {"rid": "linux-riscv64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libHarfBuzzSharp.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.macOS/8.3.1.1": {"runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.WebAssembly/8.3.1.1": {}, "HarfBuzzSharp.NativeAssets.Win32/8.3.1.1": {"runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "LiteDB/5.0.21": {"dependencies": {"System.Buffers": "4.5.1"}, "runtime": {"lib/netstandard2.0/LiteDB.dll": {"assemblyVersion": "5.0.21.0", "fileVersion": "5.0.21.0"}}}, "MicroCom.Runtime/0.11.0": {"runtime": {"lib/net5.0/MicroCom.Runtime.dll": {"assemblyVersion": "0.11.0.0", "fileVersion": "0.11.0.0"}}}, "Microsoft.DotNet.ILCompiler/9.0.7": {}, "Microsoft.NET.ILLink.Tasks/9.0.7": {}, "SkiaSharp/2.88.9": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.9.0"}}}, "SkiaSharp.NativeAssets.Linux/2.88.9": {"dependencies": {"SkiaSharp": "2.88.9"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.WebAssembly/2.88.9": {}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.Buffers/4.5.1": {}, "System.IO.Pipelines/8.0.0": {}, "Tmds.DBus.Protocol/0.21.2": {"dependencies": {"System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Tmds.DBus.Protocol.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}}}, "libraries": {"ECutPro/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Avalonia/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-3w1v4/wEpHFq6WWVNyyrne2Jyz5bdQvnK3AW36rCto42L+AtdbnO/SG2SIqJ7ObXJl+Y3LXz2XDGx9Blzqduow==", "path": "avalonia/11.3.2", "hashPath": "avalonia.11.3.2.nupkg.sha512"}, "Avalonia.Angle.Windows.Natives/2.1.25547.20250602": {"type": "package", "serviceable": true, "sha512": "sha512-ZL0VLc4s9rvNNFt19Pxm5UNAkmKNylugAwJPX9ulXZ6JWs/l6XZihPWWTyezaoNOVyEPU8YbURtW7XMAtqXH5A==", "path": "avalonia.angle.windows.natives/2.1.25547.20250602", "hashPath": "avalonia.angle.windows.natives.2.1.25547.20250602.nupkg.sha512"}, "Avalonia.BuildServices/0.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-KmCN6Hc+45q4OnF10ge450yVUvWuxU6bdQiyKqiSvrHKpahNrEdk0kG6Ip6GHk2SKOCttGQuA206JVdkldEENg==", "path": "avalonia.buildservices/0.0.31", "hashPath": "avalonia.buildservices.0.0.31.nupkg.sha512"}, "Avalonia.Controls.ColorPicker/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-K72rpZb6nSDuKZtcj1cfIAqpTkduff3Ng3+O22MxKhmmRDcFO0GAz7kwEArbtJTC4SNlSezaCyx6XMdNvaMcPA==", "path": "avalonia.controls.colorpicker/11.3.2", "hashPath": "avalonia.controls.colorpicker.11.3.2.nupkg.sha512"}, "Avalonia.Desktop/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-b2ZPZ60UN0Uib3ZxFrf+ixYiJTBFaXrpQAcXTSUKTDw0nAU29sbfjvdtpBkBd6+idWpNtI+GhOjf0Mw0v1ncQg==", "path": "avalonia.desktop/11.3.2", "hashPath": "avalonia.desktop.11.3.2.nupkg.sha512"}, "Avalonia.Diagnostics/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-3f4+uGJTrBCY9mxMy7690s8WKqyF4I29VFEUSASV5nVX6kyv/d1+OHBNd0GMyuyPOf+eEEA1ylNSHhRCw3jsvw==", "path": "avalonia.diagnostics/11.3.2", "hashPath": "avalonia.diagnostics.11.3.2.nupkg.sha512"}, "Avalonia.Fonts.Inter/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-gOijv2Tu8w6XurxYWSC4OdFycYbDooZfTcETXEx45syl710wpG+0aEHNFGlQyyOqqxGQHFbqxxwX6GxumD8b/g==", "path": "avalonia.fonts.inter/11.3.2", "hashPath": "avalonia.fonts.inter.11.3.2.nupkg.sha512"}, "Avalonia.FreeDesktop/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-8laGOvPM83kB4xUqXGIRxN61Q5Ux/97URL7CU2Dizh4Nm7Ydj9+urKLTylk6dxWG7kZlo4e4k9G2aVUPkxTBGA==", "path": "avalonia.freedesktop/11.3.2", "hashPath": "avalonia.freedesktop.11.3.2.nupkg.sha512"}, "Avalonia.Native/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-dv3PVUcClxeDlNSoBjTdNuRYXdbzT3BgtpjEX/fv3pVreKfbh39wWQ+n4ecGh0FUKIlcj0X8BEaQ83t5eRsLnA==", "path": "avalonia.native/11.3.2", "hashPath": "avalonia.native.11.3.2.nupkg.sha512"}, "Avalonia.Remote.Protocol/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-my6aXePR+N7tl3xDKdYNH2ZFZWUSNmyeU37HIgYJX2fQ4IOBv7SeaWBEd1F/qVsIbOlYxaqE7qOPfeFFtdYq/A==", "path": "avalonia.remote.protocol/11.3.2", "hashPath": "avalonia.remote.protocol.11.3.2.nupkg.sha512"}, "Avalonia.Skia/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-PJIPSqkWbQpKyWPMEIDAWYQaJ8xpJASg2H75sGWCkthLj+mCG/DZxdHI6UsDAcxKu/ppSRkX88RGo117bRYfFg==", "path": "avalonia.skia/11.3.2", "hashPath": "avalonia.skia.11.3.2.nupkg.sha512"}, "Avalonia.Themes.Fluent/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-aRCCmFSumpHJOxsXbdUUc/AaJ/O1HLggfvSxtXYm84QCutuP4OCqgxP9Pka1hb1+2e/TQxfNqM/6KRKPt4SaRg==", "path": "avalonia.themes.fluent/11.3.2", "hashPath": "avalonia.themes.fluent.11.3.2.nupkg.sha512"}, "Avalonia.Themes.Simple/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0TnR6vVS5qStClhG0T9i5Q7jSlZxqrpBxzBb7HPzNqe8dkNfM6VTN38J82sggvZAVddqSc3XRrB+VgheChxKQw==", "path": "avalonia.themes.simple/11.3.2", "hashPath": "avalonia.themes.simple.11.3.2.nupkg.sha512"}, "Avalonia.Win32/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-CpM6zBwDwMFKw9/iyj0d8jxXKLZena/HFblS9Oc7BmLH8qxe8icr7ZBI5rElebrxDA5O590dAtfWdtOvB1/AZQ==", "path": "avalonia.win32/11.3.2", "hashPath": "avalonia.win32.11.3.2.nupkg.sha512"}, "Avalonia.X11/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-MEMXOIaAr6jMtl9BhJ8sj7Vz+z19dWfcryWksV35LsolQhQmjmzJPVcCppdnvm5HxkohUXB1qN1RqWN1cqRwBQ==", "path": "avalonia.x11/11.3.2", "hashPath": "avalonia.x11.11.3.2.nupkg.sha512"}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "path": "communitytoolkit.mvvm/8.4.0", "hashPath": "communitytoolkit.mvvm.8.4.0.nupkg.sha512"}, "HarfBuzzSharp/8.3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-tLZN66oe/uiRPTZfrCU4i8ScVGwqHNh5MHrXj0yVf4l7Mz0FhTGnQ71RGySROTmdognAs0JtluHkL41pIabWuQ==", "path": "harfbuzzsharp/8.3.1.1", "hashPath": "harfbuzzsharp.8.3.1.1.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Linux/8.3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-3EZ1mpIiKWRLL5hUYA82ZHteeDIVaEA/Z0rA/wU6tjx6crcAkJnBPwDXZugBSfo8+J3EznvRJf49uMsqYfKrHg==", "path": "harfbuzzsharp.nativeassets.linux/8.3.1.1", "hashPath": "harfbuzzsharp.nativeassets.linux.8.3.1.1.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/8.3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-jbtCsgftcaFLCA13tVKo5iWdElJScrulLTKJre36O4YQTIlwDtPPqhRZNk+Y0vv4D1gxbscasGRucUDfS44ofQ==", "path": "harfbuzzsharp.nativeassets.macos/8.3.1.1", "hashPath": "harfbuzzsharp.nativeassets.macos.8.3.1.1.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.WebAssembly/8.3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-loJweK2u/mH/3C2zBa0ggJlITIszOkK64HLAZB7FUT670dTg965whLFYHDQo69NmC4+d9UN0icLC9VHidXaVCA==", "path": "harfbuzzsharp.nativeassets.webassembly/8.3.1.1", "hashPath": "harfbuzzsharp.nativeassets.webassembly.8.3.1.1.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/8.3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-UsJtQsfAJoFDZrXc4hCUfRPMqccfKZ0iumJ/upcUjz/cmsTgVFGNEL5yaJWmkqsuFYdMWbj/En5/kS4PFl9hBA==", "path": "harfbuzzsharp.nativeassets.win32/8.3.1.1", "hashPath": "harfbuzzsharp.nativeassets.win32.8.3.1.1.nupkg.sha512"}, "LiteDB/5.0.21": {"type": "package", "serviceable": true, "sha512": "sha512-ykJ7ffFl7P9YQKR/PLci6zupiLrsSCNkOTiw6OtzntH7d2kCYp5L1+3a/pksKgTEHcJBoPXFtg7VZSGVBseN9w==", "path": "litedb/5.0.21", "hashPath": "litedb.5.0.21.nupkg.sha512"}, "MicroCom.Runtime/0.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "path": "microcom.runtime/0.11.0", "hashPath": "microcom.runtime.0.11.0.nupkg.sha512"}, "Microsoft.DotNet.ILCompiler/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-FpvJ2rCpj2CB/4hZt0cmB3hCPMNZV2vrisQis3hR9itTEjLzeggi0AShrgJIZ5+3Y7433K7ESSsJ6+8T+nE2nQ==", "path": "microsoft.dotnet.ilcompiler/9.0.7", "hashPath": "microsoft.dotnet.ilcompiler.9.0.7.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-SZ1brSGoLnhLbE8QUZrtN6YwzN2gDT1wbx9qDBEfFFJcstiDTjJ6ygNuTPBV/K7SjGfx2YNbcJi5+ygbPOZpDg==", "path": "microsoft.net.illink.tasks/9.0.7", "hashPath": "microsoft.net.illink.tasks.9.0.7.nupkg.sha512"}, "SkiaSharp/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-3MD5VHjXXieSHCleRLuaTXmL2pD0mB7CcOB1x2kA1I4bhptf4e3R27iM93264ZYuAq6mkUyX5XbcxnZvMJYc1Q==", "path": "skiasharp/2.88.9", "hashPath": "skiasharp.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-cWSaJKVPWAaT/WIn9c8T5uT/l4ETwHxNJTkEOtNKjphNo8AW6TF9O32aRkxqw3l8GUdUo66Bu7EiqtFh/XG0Zg==", "path": "skiasharp.nativeassets.linux/2.88.9", "hashPath": "skiasharp.nativeassets.linux.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-Nv5spmKc4505Ep7oUoJ5vp3KweFpeNqxpyGDWyeEPTX2uR6S6syXIm3gj75dM0YJz7NPvcix48mR5laqs8dPuA==", "path": "skiasharp.nativeassets.macos/2.88.9", "hashPath": "skiasharp.nativeassets.macos.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.WebAssembly/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-kt06RccBHSnAs2wDYdBSfsjIDbY3EpsOVqnlDgKdgvyuRA8ZFDaHRdWNx1VHjGgYzmnFCGiTJBnXFl5BqGwGnA==", "path": "skiasharp.nativeassets.webassembly/2.88.9", "hashPath": "skiasharp.nativeassets.webassembly.2.88.9.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.9": {"type": "package", "serviceable": true, "sha512": "sha512-wb2kYgU7iy84nQLYZwMeJXixvK++GoIuECjU4ECaUKNuflyRlJKyiRhN1MAHswvlvzuvkrjRWlK0Za6+kYQK7w==", "path": "skiasharp.nativeassets.win32/2.88.9", "hashPath": "skiasharp.nativeassets.win32.2.88.9.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "Tmds.DBus.Protocol/0.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-ScSMrUrrw8px4kK1Glh0fZv/HQUlg1078bNXNPfRPKQ3WbRzV9HpsydYEOgSoMK5LWICMf2bMwIFH0pGjxjcMA==", "path": "tmds.dbus.protocol/0.21.2", "hashPath": "tmds.dbus.protocol.0.21.2.nupkg.sha512"}}}